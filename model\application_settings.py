import json
import os
import sqlite3
from datetime import datetime

from model.base_model import BaseModel


class ApplicationSettings(BaseModel):
    """Model for managing application-wide settings and preferences"""

    def __init__(self, db_path):
        super().__init__(db_path)
        self._table_name = "application_settings"
        self._create_tables()

    def table_name(self):
        return self._table_name

    def fields(self):
        return [
            'id', 'setting_key', 'setting_value', 'setting_type', 'category',
            'description', 'default_value', 'created_at', 'updated_at'
        ]

    def primary_key(self):
        return 'id'

    def _create_tables(self):
        """Create application settings tables"""
        # Create main settings table
        settings_query = '''
        CREATE TABLE IF NOT EXISTS application_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key TEXT UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type TEXT NOT NULL DEFAULT 'string',
            category TEXT NOT NULL DEFAULT 'general',
            description TEXT,
            default_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        '''
        self.execute_query(settings_query)

        # Create user preferences table
        user_preferences_query = '''
        CREATE TABLE IF NOT EXISTS user_preferences (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT,
            preference_key TEXT NOT NULL,
            preference_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(user_id, preference_key)
        )
        '''
        self.execute_query(user_preferences_query)

        # Create backup settings table
        backup_settings_query = '''
        CREATE TABLE IF NOT EXISTS backup_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            backup_enabled BOOLEAN DEFAULT 1,
            backup_frequency TEXT DEFAULT 'daily',
            backup_location TEXT,
            auto_backup_count INTEGER DEFAULT 7,
            last_backup_date TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        '''
        self.execute_query(backup_settings_query)

        # Initialize default settings
        self._initialize_default_settings()

    def _initialize_default_settings(self):
        """Initialize default application settings"""
        default_settings = [
            # Appearance Settings
            ('theme', 'cosmo', 'string', 'appearance', 'Application theme', 'cosmo'),
            ('font_family', 'Segoe UI', 'string', 'appearance', 'Default font family', 'Segoe UI'),
            ('font_size', '10', 'integer', 'appearance', 'Default font size', '10'),
            ('window_width', '1200', 'integer', 'appearance', 'Default window width', '1200'),
            ('window_height', '800', 'integer', 'appearance', 'Default window height', '800'),
            ('maximize_on_startup', 'false', 'boolean', 'appearance', 'Maximize window on startup', 'false'),

            # Regional Settings
            ('date_format', '%Y-%m-%d', 'string', 'regional', 'Date display format', '%Y-%m-%d'),
            ('time_format', '%H:%M:%S', 'string', 'regional', 'Time display format', '%H:%M:%S'),
            ('currency_symbol', '$', 'string', 'regional', 'Default currency symbol', '$'),
            ('currency_position', 'before', 'string', 'regional', 'Currency symbol position', 'before'),
            ('decimal_places', '2', 'integer', 'regional', 'Decimal places for currency', '2'),
            ('thousands_separator', ',', 'string', 'regional', 'Thousands separator', ','),
            ('decimal_separator', '.', 'string', 'regional', 'Decimal separator', '.'),
            ('language', 'en', 'string', 'regional', 'Application language', 'en'),

            # Behavior Settings
            ('auto_save', 'true', 'boolean', 'behavior', 'Auto-save forms', 'true'),
            ('confirm_delete', 'true', 'boolean', 'behavior', 'Confirm before deleting', 'true'),
            ('show_tooltips', 'true', 'boolean', 'behavior', 'Show tooltips', 'true'),
            ('remember_window_position', 'true', 'boolean', 'behavior', 'Remember window position', 'true'),
            ('startup_company', '', 'string', 'behavior', 'Default company to open', ''),
            ('recent_files_count', '10', 'integer', 'behavior', 'Number of recent files to remember', '10'),

            # Data Settings
            ('default_account_type', 'Checking Account', 'string', 'data', 'Default account type', 'Checking Account'),
            ('default_transaction_type', 'Expense', 'string', 'data', 'Default transaction type', 'Expense'),
            ('fiscal_year_start', '01-01', 'string', 'data', 'Fiscal year start (MM-DD)', '01-01'),
            ('enable_categories', 'true', 'boolean', 'data', 'Enable transaction categories', 'true'),
            ('enable_projects', 'true', 'boolean', 'data', 'Enable project tracking', 'true'),

            # Security Settings
            ('session_timeout', '30', 'integer', 'security', 'Session timeout in minutes', '30'),
            ('password_min_length', '6', 'integer', 'security', 'Minimum password length', '6'),
            ('require_password_change', 'false', 'boolean', 'security', 'Require periodic password change', 'false'),
            ('enable_audit_log', 'true', 'boolean', 'security', 'Enable audit logging', 'true'),

            # Backup Settings
            ('backup_enabled', 'true', 'boolean', 'backup', 'Enable automatic backups', 'true'),
            ('backup_frequency', 'daily', 'string', 'backup', 'Backup frequency', 'daily'),
            ('backup_location', './backups', 'string', 'backup', 'Backup location', './backups'),
            ('backup_retention_days', '30', 'integer', 'backup', 'Backup retention in days', '30'),
        ]

        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            for setting_key, default_value, setting_type, category, description, _ in default_settings:
                # Check if setting already exists
                cursor.execute('SELECT id FROM application_settings WHERE setting_key = ?', (setting_key,))
                if not cursor.fetchone():
                    cursor.execute('''
                        INSERT INTO application_settings
                        (setting_key, setting_value, setting_type, category, description, default_value)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (setting_key, default_value, setting_type, category, description, default_value))

            conn.commit()
        except sqlite3.Error as e:
            print(f"Error initializing default settings: {e}")
        finally:
            conn.close()

    def get_setting(self, setting_key, default_value=None):
        """Get a specific setting value"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT setting_value, setting_type FROM application_settings
                WHERE setting_key = ?
            ''', (setting_key,))

            result = cursor.fetchone()
            conn.close()

            if result:
                value, setting_type = result
                return self._convert_setting_value(value, setting_type)

            return default_value

        except sqlite3.Error as e:
            print(f"Error getting setting {setting_key}: {e}")
            return default_value

    def set_setting(self, setting_key, setting_value, setting_type='string'):
        """Set a specific setting value"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            # Convert value to string for storage
            value_str = str(setting_value)

            cursor.execute('''
                INSERT OR REPLACE INTO application_settings
                (setting_key, setting_value, setting_type, updated_at)
                VALUES (?, ?, ?, ?)
            ''', (setting_key, value_str, setting_type, datetime.now()))

            conn.commit()
            conn.close()
            return True

        except sqlite3.Error as e:
            print(f"Error setting {setting_key}: {e}")
            return False

    def get_settings_by_category(self, category):
        """Get all settings for a specific category"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT setting_key, setting_value, setting_type, description, default_value
                FROM application_settings
                WHERE category = ?
                ORDER BY setting_key
            ''', (category,))

            settings = {}
            for row in cursor.fetchall():
                key, value, setting_type, description, default_value = row
                settings[key] = {
                    'value': self._convert_setting_value(value, setting_type),
                    'type': setting_type,
                    'description': description,
                    'default': self._convert_setting_value(default_value, setting_type)
                }

            conn.close()
            return settings

        except sqlite3.Error as e:
            print(f"Error getting settings for category {category}: {e}")
            return {}

    def get_all_settings(self):
        """Get all application settings grouped by category"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT category, setting_key, setting_value, setting_type, description, default_value
                FROM application_settings
                ORDER BY category, setting_key
            ''')

            settings = {}
            for row in cursor.fetchall():
                category, key, value, setting_type, description, default_value = row

                if category not in settings:
                    settings[category] = {}

                settings[category][key] = {
                    'value': self._convert_setting_value(value, setting_type),
                    'type': setting_type,
                    'description': description,
                    'default': self._convert_setting_value(default_value, setting_type)
                }

            conn.close()
            return settings

        except sqlite3.Error as e:
            print(f"Error getting all settings: {e}")
            return {}

    def reset_to_defaults(self, category=None):
        """Reset settings to default values"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            cursor = conn.cursor()

            if category:
                cursor.execute('''
                    UPDATE application_settings
                    SET setting_value = default_value, updated_at = ?
                    WHERE category = ?
                ''', (datetime.now(), category))
            else:
                cursor.execute('''
                    UPDATE application_settings
                    SET setting_value = default_value, updated_at = ?
                ''', (datetime.now(),))

            conn.commit()
            conn.close()
            return True

        except sqlite3.Error as e:
            print(f"Error resetting settings: {e}")
            return False

    def export_settings(self, file_path):
        """Export settings to a JSON file"""
        try:
            settings = self.get_all_settings()

            # Convert to exportable format
            export_data = {
                'export_date': datetime.now().isoformat(),
                'settings': settings
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            return True

        except Exception as e:
            print(f"Error exporting settings: {e}")
            return False

    def import_settings(self, file_path):
        """Import settings from a JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)

            settings = import_data.get('settings', {})

            for category, category_settings in settings.items():
                for key, setting_info in category_settings.items():
                    self.set_setting(key, setting_info['value'], setting_info['type'])

            return True

        except Exception as e:
            print(f"Error importing settings: {e}")
            return False

    def _convert_setting_value(self, value, setting_type):
        """Convert string value to appropriate type"""
        if value is None:
            return None

        try:
            if setting_type == 'boolean':
                return value.lower() in ('true', '1', 'yes', 'on')
            elif setting_type == 'integer':
                return int(value)
            elif setting_type == 'float':
                return float(value)
            elif setting_type == 'json':
                return json.loads(value)
            else:  # string
                return str(value)
        except (ValueError, json.JSONDecodeError):
            return value

    def get_available_themes(self):
        """Get list of available themes"""
        return [
            'cosmo', 'flatly', 'journal', 'litera', 'lumen', 'minty',
            'pulse', 'sandstone', 'united', 'yeti', 'morph', 'simplex',
            'cerculean', 'solar', 'superhero', 'darkly', 'cyborg', 'vapor'
        ]

    def get_available_languages(self):
        """Get list of available languages"""
        return {
            'en': 'English',
            'es': 'Español',
            'fr': 'Français',
            'de': 'Deutsch',
            'it': 'Italiano',
            'pt': 'Português',
            'zh': '中文',
            'ja': '日本語'
        }

    def get_date_formats(self):
        """Get list of available date formats"""
        return {
            '%Y-%m-%d': '2025-01-28 (ISO)',
            '%d/%m/%Y': '28/01/2025 (DD/MM/YYYY)',
            '%m/%d/%Y': '01/28/2025 (MM/DD/YYYY)',
            '%d-%m-%Y': '28-01-2025 (DD-MM-YYYY)',
            '%B %d, %Y': 'January 28, 2025',
            '%d %B %Y': '28 January 2025'
        }


class SettingsManager:
    """Singleton class to manage application settings throughout the app"""

    _instance = None
    _settings_model = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SettingsManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self._settings_model is None:
            self._settings_model = ApplicationSettings("settings.db")

    def get(self, key, default=None):
        """Get a setting value"""
        return self._settings_model.get_setting(key, default)

    def set(self, key, value, setting_type='string'):
        """Set a setting value"""
        return self._settings_model.set_setting(key, value, setting_type)

    def get_theme(self):
        """Get current theme"""
        return self.get('theme', 'cosmo')

    def get_font_family(self):
        """Get current font family"""
        return self.get('font_family', 'Segoe UI')

    def get_font_size(self):
        """Get current font size"""
        return self.get('font_size', 10)

    def get_date_format(self):
        """Get current date format"""
        return self.get('date_format', '%Y-%m-%d')

    def get_currency_symbol(self):
        """Get current currency symbol"""
        return self.get('currency_symbol', '$')

    def get_decimal_places(self):
        """Get number of decimal places for currency"""
        return self.get('decimal_places', 2)

    def format_currency(self, amount):
        """Format amount as currency using current settings"""
        symbol = self.get_currency_symbol()
        places = self.get_decimal_places()
        position = self.get('currency_position', 'before')

        formatted_amount = f"{amount:.{places}f}"

        if position == 'before':
            return f"{symbol}{formatted_amount}"
        else:
            return f"{formatted_amount}{symbol}"

    def format_date(self, date_obj):
        """Format date using current date format setting"""
        date_format = self.get_date_format()
        return date_obj.strftime(date_format)

    def should_auto_save(self):
        """Check if auto-save is enabled"""
        return self.get('auto_save', True)

    def should_confirm_delete(self):
        """Check if delete confirmation is enabled"""
        return self.get('confirm_delete', True)

    def should_show_tooltips(self):
        """Check if tooltips should be shown"""
        return self.get('show_tooltips', True)

    def get_window_size(self):
        """Get default window size"""
        width = self.get('window_width', 1200)
        height = self.get('window_height', 800)
        return width, height

    def should_maximize_on_startup(self):
        """Check if window should be maximized on startup"""
        return self.get('maximize_on_startup', False)

    def get_backup_settings(self):
        """Get backup settings"""
        return {
            'enabled': self.get('backup_enabled', True),
            'frequency': self.get('backup_frequency', 'daily'),
            'location': self.get('backup_location', './backups'),
            'retention_days': self.get('backup_retention_days', 30)
        }
