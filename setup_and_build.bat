@echo off
echo ===================================================
echo Cashbook Application Setup and Build Script
echo ===================================================
echo.

echo Step 1: Installing required dependencies...
pip install -r requirements.txt
if %ERRORLEVEL% neq 0 (
    echo Error installing dependencies. Please check your Python installation.
    pause
    exit /b 1
)
echo Dependencies installed successfully.
echo.

echo Step 2: Creating application icon...
python create_icon.py
if %ERRORLEVEL% neq 0 (
    echo Warning: Failed to create icon. Using default icon.
    echo.
)

echo Step 3: Building the executable...
pyinstaller --clean cashbook.spec
if %ERRORLEVEL% neq 0 (
    echo Error building the executable. Please check the output for details.
    pause
    exit /b 1
)
echo.
echo Executable built successfully!
echo.

echo Step 4: Checking for Inno Setup...
reg query "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Inno Setup 6_is1" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Inno Setup not found. Please install Inno Setup to create the installer.
    echo You can download it from: https://jrsoftware.org/isdl.php
    echo.
    echo After installing Inno Setup, run:
    echo   "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" cashbook_installer.iss
    echo.
    echo Or open cashbook_installer.iss with Inno Setup Compiler and click Build.
) else (
    echo Inno Setup found. Creating installer...
    "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" cashbook_installer.iss
    if %ERRORLEVEL% neq 0 (
        echo Error creating the installer. Please check the output for details.
    ) else (
        echo.
        echo Installer created successfully!
        echo The installer is located in the Output directory.
    )
)

echo.
echo ===================================================
echo Build process completed!
echo ===================================================
echo.
echo If you encounter any issues, please refer to DISTRIBUTION_GUIDE.md
echo for troubleshooting information.
echo.
pause
