import os
import sys
import tkinter as tk
from tkinter import messagebox

import ttkbootstrap as ttk
from ttkbootstrap.constants import *

# Import our modules
from model.database import Database
from model.transaction import TransactionManager
from view.admin_frame import AdminFrame
from view.client_frame import ClientFrame
from view.company_frame import CompanyFrame
from view.login_frame import LoginFrame
from view.main_app import MainApplication
from view.reports_frame import ReportsFrame


class CashbookApp:
    def __init__(self, root):
        self.root = root
        self.db = Database()
        self.current_user = None
        self.current_role = None
        self.current_company = None
        self.transaction_manager = None

        # Set up error handling for tkinter callbacks
        self.root.report_callback_exception = self.show_error

        # Create the main application container
        self.main_app = MainApplication(root, self)

        # Create screens
        self.login_frame = LoginFrame(self.main_app, self.authenticate_user)
        self.main_app.add_screen("login", self.login_frame)

        # Start with login screen
        self.show_login()

    def show_error(self, exc_type, exc_value, exc_traceback):
        """Handle uncaught exceptions in tkinter callbacks"""
        import traceback
        error_message = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))

        error_window = ttk.Toplevel(self.root)
        error_window.title("Error")
        error_window.geometry("500x300")

        error_label = ttk.Label(error_window, text="An error occurred:", font=("Helvetica", 12, "bold"))
        error_label.pack(pady=(10, 5))

        error_text = ttk.Text(error_window, wrap="word", height=10, width=60)
        error_text.insert("1.0", error_message)
        error_text.config(state="disabled")
        error_text.pack(padx=10, pady=5, fill="both", expand=True)

        close_button = ttk.Button(error_window, text="Close", bootstyle=DANGER, command=error_window.destroy)
        close_button.pack(pady=10)

        print(f"Error: {error_message}", file=sys.stderr)

    def show_login(self):
        # Show login screen
        self.main_app.show_screen("login")
        self.login_frame.focus_username()

    def authenticate_user(self, username, password):
        role = self.db.authenticate_user(username, password)

        if role:
            self.current_user = username
            self.current_role = role

            if role == "Admin":
                self.show_admin_dashboard()
            else:
                self.show_client_dashboard()

            return True

        return False

    def logout(self):
        self.current_user = None
        self.current_role = None
        self.current_company = None
        self.transaction_manager = None
        self.show_login()

    def show_admin_dashboard(self):
        # Create admin dashboard if it doesn't exist
        if "admin_dashboard" not in self.main_app.screens:
            admin_frame = AdminFrame(self.main_app, self.current_user, self.logout, self.db)
            self.main_app.add_screen("admin_dashboard", admin_frame)

        # Show admin dashboard
        self.main_app.show_screen("admin_dashboard")

    def show_client_dashboard(self):
        # Create client dashboard if it doesn't exist
        if "client_dashboard" not in self.main_app.screens:
            client_frame = ClientFrame(
                self.main_app,
                self.current_user,
                self.logout,
                self.create_company,
                self.open_company
            )
            self.main_app.add_screen("client_dashboard", client_frame)
        else:
            # Refresh the company list
            self.main_app.screens["client_dashboard"].load_companies()

        # Show client dashboard
        self.main_app.show_screen("client_dashboard")

    def create_company(self):
        # Show a dialog to create a new company
        create_window = ttk.Toplevel(self.root)
        create_window.title("Create New Company")
        create_window.geometry("400x200")
        create_window.transient(self.root)
        create_window.grab_set()

        frame = ttk.Frame(create_window, padding=20)
        frame.pack(fill="both", expand=True)

        ttk.Label(frame, text="Company Name:").grid(row=0, column=0, sticky="w", pady=10)
        name_var = tk.StringVar()
        name_entry = ttk.Entry(frame, textvariable=name_var, width=30)
        name_entry.grid(row=0, column=1, sticky="ew", pady=10)

        ttk.Label(frame, text="Description (optional):").grid(row=1, column=0, sticky="w", pady=10)
        desc_var = tk.StringVar()
        desc_entry = ttk.Entry(frame, textvariable=desc_var, width=30)
        desc_entry.grid(row=1, column=1, sticky="ew", pady=10)

        button_frame = ttk.Frame(frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=15)

        def save_company():
            company_name = name_var.get().strip()

            if not company_name:
                messagebox.showerror(title="Error",
                                      message="Company name is required",
                                      parent=create_window)
                return

            try:
                # Create company database
                self.db.init_company_db(company_name)

                create_window.destroy()

                # Open the new company
                self.open_company(company_name)

            except Exception as e:
                messagebox.showerror(title="Error",
                                      message=f"Failed to create company: {str(e)}",
                                      parent=create_window)

        save_button = ttk.Button(button_frame, text="Create", bootstyle=SUCCESS, command=save_company)
        save_button.pack(side="left", padx=5)

        cancel_button = ttk.Button(button_frame, text="Cancel", bootstyle=SECONDARY,
                                  command=create_window.destroy)
        cancel_button.pack(side="left", padx=5)

        # Focus on name entry
        name_entry.focus_set()

    def open_company(self, company_name):
        # Convert company name to database file name
        db_name = f"{company_name.lower().replace(' ', '_')}.db"

        if not os.path.exists(db_name):
            messagebox.showerror(title="Error",
                                message=f"Company database for '{company_name}' not found")
            return

        # Set current company and create transaction manager
        self.current_company = company_name
        self.transaction_manager = TransactionManager(db_name)

        # Create company frame
        company_key = f"company_{company_name}"
        if company_key in self.main_app.screens:
            # Remove existing company frame
            self.main_app.remove_screen(company_key)

        # Create new company frame
        company_frame = CompanyFrame(self.main_app, company_name, self.transaction_manager, self.close_company)
        self.main_app.add_screen(company_key, company_frame)

        # Show company frame
        self.main_app.show_screen(company_key)

    def close_company(self):
        self.current_company = None
        self.transaction_manager = None

        if self.current_role == "Admin":
            self.show_admin_dashboard()
        else:
            self.show_client_dashboard()

    def show_reports(self, company_name=None):
        """Show reports for the current or specified company"""
        if not company_name and not self.current_company:
            messagebox.showerror("Error", "No company is currently open")
            return

        company_name = company_name or self.current_company
        db_path = self.db.get_company_db_path(company_name)

        # Create reports key
        reports_key = f"reports_{company_name}"

        # Create new reports frame if it doesn't exist
        if reports_key not in self.main_app.screens:
            reports_frame = ReportsFrame(
                self.main_app,
                company_name,
                db_path,
                lambda: self.main_app.show_screen(f"company_{company_name}")
            )
            self.main_app.add_screen(reports_key, reports_frame)

        # Show reports frame
        self.main_app.show_screen(reports_key)

if __name__ == "__main__":
    # Create a ttkbootstrap window
    root = ttk.Window(themename="litera")
    root.title("Cashbook Software")
    root.geometry("1000x700")
    root.minsize(800, 600)  # Set minimum window size

    app = CashbookApp(root)

    root.mainloop()

# -*- coding: utf-8 -*-