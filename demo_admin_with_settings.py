#!/usr/bin/env python3
"""
Demo script showing Application Settings integrated into Admin interface
"""

import tkinter as tk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import os
import sys

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from view.admin_frame import AdminFrame


class MockDatabase:
    """Mock database for demo purposes"""
    
    def get_all_users(self):
        return [
            {"id": 1, "username": "admin", "role": "Admin"},
            {"id": 2, "username": "john_doe", "role": "Client"},
            {"id": 3, "username": "jane_smith", "role": "Client"},
            {"id": 4, "username": "manager", "role": "Admin"}
        ]
    
    def add_user(self, username, password, role):
        print(f"Demo: Adding user '{username}' with role '{role}'")
        return True
    
    def update_user(self, user_id, data):
        print(f"Demo: Updating user {user_id}")
        return True
    
    def delete_user(self, user_id):
        print(f"Demo: Deleting user {user_id}")
        return True


class AdminSettingsDemo:
    def __init__(self):
        # Create main window
        self.root = ttk.Window(themename="cosmo")
        self.root.title("Admin Dashboard with Application Settings")
        self.root.geometry("1400x900")
        
        # Create mock database
        self.mock_db = MockDatabase()
        
        # Create admin frame
        self.admin_frame = AdminFrame(
            parent=self.root,
            username="admin",
            logout_callback=self.logout,
            db=self.mock_db
        )
        
        self.admin_frame.pack(fill="both", expand=True)
        
        # Show welcome dialog
        self.root.after(500, self.show_welcome_dialog)
        
    def show_welcome_dialog(self):
        """Show welcome dialog with instructions"""
        welcome = tk.Toplevel(self.root)
        welcome.title("Admin Settings Demo")
        welcome.geometry("600x500")
        welcome.transient(self.root)
        welcome.grab_set()
        
        # Center the dialog
        welcome.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 400,
            self.root.winfo_rooty() + 200
        ))
        
        frame = ttk.Frame(welcome, padding=20)
        frame.pack(fill="both", expand=True)
        
        # Title
        title = ttk.Label(
            frame,
            text="🔧 Admin Dashboard with Application Settings",
            font=("Segoe UI", 16, "bold")
        )
        title.pack(pady=(0, 20))
        
        # Instructions
        instructions = """
Welcome to the Admin Dashboard Demo!

This demo shows how Application Settings are integrated 
into the Admin interface with proper access control.

🔒 ADMIN-ONLY FEATURES:

1. Application Settings Tab
   • Click on the "Application Settings" tab to access all settings
   • Configure appearance, regional, behavior, data, security, and backup settings
   
2. Quick Access Settings Button
   • Click the "⚙️ Settings" button in the header for instant access
   • Automatically switches to the Application Settings tab

3. Settings Categories Available:
   • 🎨 Appearance: Themes, fonts, window settings
   • 🌍 Regional: Date formats, currency, language
   • ⚙️ Behavior: Auto-save, confirmations, tooltips
   • 📊 Data: Default values, features
   • 🔒 Security: Session, passwords, audit
   • 💾 Backup: Automatic backup configuration

4. Settings Management:
   • Save settings permanently
   • Reset to defaults
   • Import/Export settings (JSON format)
   • Real-time preview of changes

🚫 CLIENT USERS CANNOT ACCESS THESE SETTINGS
Only administrators have access to application-wide settings.

Try the different features and see how settings are applied!
        """
        
        instructions_label = ttk.Label(
            frame,
            text=instructions.strip(),
            font=("Segoe UI", 10),
            justify="left"
        )
        instructions_label.pack(pady=(0, 20))
        
        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill="x")
        
        # Start demo button
        start_button = ttk.Button(
            button_frame,
            text="Start Demo",
            command=welcome.destroy,
            bootstyle=PRIMARY
        )
        start_button.pack(side="left")
        
        # Quick test button
        test_button = ttk.Button(
            button_frame,
            text="Test Settings Button",
            command=lambda: self.test_settings_access(welcome),
            bootstyle=INFO
        )
        test_button.pack(side="left", padx=(10, 0))
        
        # Close button
        close_button = ttk.Button(
            button_frame,
            text="Close Demo",
            command=self.root.quit,
            bootstyle=SECONDARY
        )
        close_button.pack(side="right")
    
    def test_settings_access(self, welcome_window):
        """Test the settings access functionality"""
        try:
            # Close welcome dialog
            welcome_window.destroy()
            
            # Test the quick access button
            self.admin_frame.quick_access_settings()
            
            # Show confirmation
            self.root.after(100, lambda: messagebox.showinfo(
                "Settings Access Test",
                "✅ SUCCESS!\n\n"
                "The Application Settings tab is now active.\n"
                "You can see all the settings categories and options.\n\n"
                "This demonstrates that:\n"
                "• Settings are properly integrated into admin interface\n"
                "• Quick access button works correctly\n"
                "• Admin users have full access to all settings"
            ))
            
        except Exception as e:
            messagebox.showerror(
                "Test Error",
                f"❌ FAILED!\n\nError: {str(e)}\n\n"
                "This indicates an issue with the settings integration."
            )
    
    def logout(self):
        """Handle logout"""
        result = messagebox.askyesno(
            "Logout Confirmation",
            "Are you sure you want to logout?\n\n"
            "This will close the admin dashboard demo."
        )
        if result:
            self.root.quit()
    
    def run(self):
        """Run the demo"""
        print("=" * 60)
        print("ADMIN DASHBOARD WITH APPLICATION SETTINGS DEMO")
        print("=" * 60)
        print()
        print("Features Demonstrated:")
        print("✓ Application Settings integrated into Admin interface")
        print("✓ Admin-only access control")
        print("✓ Quick access settings button")
        print("✓ Comprehensive settings categories")
        print("✓ Settings management (save, reset, import/export)")
        print()
        print("Security Features:")
        print("🔒 Settings only available to admin users")
        print("🔒 Client users cannot access application settings")
        print("🔒 Visual indicators show admin-only access")
        print()
        print("Instructions:")
        print("1. Use the welcome dialog to understand the features")
        print("2. Click 'Application Settings' tab to access settings")
        print("3. Try the '⚙️ Settings' button for quick access")
        print("4. Test different settings categories")
        print("5. Try saving, resetting, and importing/exporting settings")
        print()
        print("=" * 60)
        
        self.root.mainloop()


def main():
    """Main function"""
    print("Admin Dashboard with Application Settings Demo")
    print("=" * 50)
    
    # Check if required files exist
    required_files = [
        "view/admin_frame.py",
        "view/application_settings_frame.py",
        "model/application_settings.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ERROR: Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPlease ensure all Application Settings files are present.")
        return
    
    print("✅ All required files found")
    print("✅ Starting admin dashboard demo...")
    print()
    
    try:
        app = AdminSettingsDemo()
        app.run()
        
        print("\n" + "=" * 50)
        print("Demo completed successfully!")
        print("Application Settings are properly integrated into the Admin interface.")
        
    except Exception as e:
        print(f"\n❌ ERROR: Failed to start demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
