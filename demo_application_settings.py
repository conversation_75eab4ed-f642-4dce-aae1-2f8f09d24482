#!/usr/bin/env python3
"""
Demo script to showcase the Application Settings system
"""

import tkinter as tk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import os
from datetime import datetime

# Import the settings components
from view.application_settings_frame import ApplicationSettingsFrame
from model.application_settings import SettingsManager


class SettingsDemo:
    def __init__(self):
        # Create main window
        self.root = ttk.Window(themename="cosmo")
        self.root.title("Application Settings Demo")
        self.root.geometry("1000x700")
        
        # Initialize settings manager
        self.settings_manager = SettingsManager()
        
        # Apply current theme
        self.apply_current_theme()
        
        # Create main interface
        self.create_main_interface()
        
    def create_main_interface(self):
        """Create the main demo interface"""
        # Main container
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill="both", expand=True)
        
        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))
        
        title_label = ttk.Label(
            header_frame,
            text="Application Settings Demo",
            font=("Segoe UI", 18, "bold")
        )
        title_label.pack(side="left")
        
        # Settings button
        settings_button = ttk.Button(
            header_frame,
            text="⚙️ Open Settings",
            command=self.open_settings,
            bootstyle=PRIMARY
        )
        settings_button.pack(side="right")
        
        # Demo content area
        content_frame = ttk.LabelFrame(main_frame, text="Current Settings Demo", padding=20)
        content_frame.pack(fill="both", expand=True, pady=(0, 20))
        
        # Create demo widgets that show current settings
        self.create_demo_widgets(content_frame)
        
        # Action buttons
        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill="x")
        
        refresh_button = ttk.Button(
            action_frame,
            text="🔄 Refresh Demo",
            command=self.refresh_demo,
            bootstyle=INFO
        )
        refresh_button.pack(side="left", padx=(0, 10))
        
        theme_button = ttk.Button(
            action_frame,
            text="🎨 Apply Current Theme",
            command=self.apply_current_theme,
            bootstyle=SUCCESS
        )
        theme_button.pack(side="left", padx=(0, 10))
        
        test_button = ttk.Button(
            action_frame,
            text="🧪 Test Settings",
            command=self.test_settings,
            bootstyle=WARNING
        )
        test_button.pack(side="left")
        
        # Status
        self.status_var = tk.StringVar(value="Ready - Click 'Open Settings' to configure the application")
        status_label = ttk.Label(action_frame, textvariable=self.status_var, foreground="gray")
        status_label.pack(side="right")
        
    def create_demo_widgets(self, parent):
        """Create widgets that demonstrate current settings"""
        # Clear existing widgets
        for widget in parent.winfo_children():
            widget.destroy()
        
        # Appearance settings demo
        appearance_frame = ttk.LabelFrame(parent, text="Appearance Settings", padding=10)
        appearance_frame.pack(fill="x", pady=(0, 10))
        
        current_theme = self.settings_manager.get_theme()
        font_family = self.settings_manager.get_font_family()
        font_size = self.settings_manager.get_font_size()
        
        ttk.Label(appearance_frame, text=f"Current Theme: {current_theme}", font=(font_family, font_size)).pack(anchor="w")
        ttk.Label(appearance_frame, text=f"Font Family: {font_family}", font=(font_family, font_size)).pack(anchor="w")
        ttk.Label(appearance_frame, text=f"Font Size: {font_size}px", font=(font_family, font_size)).pack(anchor="w")
        
        # Regional settings demo
        regional_frame = ttk.LabelFrame(parent, text="Regional Settings", padding=10)
        regional_frame.pack(fill="x", pady=(0, 10))
        
        # Date format demo
        current_date = datetime.now()
        formatted_date = self.settings_manager.format_date(current_date)
        ttk.Label(regional_frame, text=f"Date Format: {formatted_date}", font=(font_family, font_size)).pack(anchor="w")
        
        # Currency format demo
        test_amount = 1234.56
        formatted_currency = self.settings_manager.format_currency(test_amount)
        ttk.Label(regional_frame, text=f"Currency Format: {formatted_currency}", font=(font_family, font_size)).pack(anchor="w")
        
        decimal_places = self.settings_manager.get_decimal_places()
        ttk.Label(regional_frame, text=f"Decimal Places: {decimal_places}", font=(font_family, font_size)).pack(anchor="w")
        
        # Behavior settings demo
        behavior_frame = ttk.LabelFrame(parent, text="Behavior Settings", padding=10)
        behavior_frame.pack(fill="x", pady=(0, 10))
        
        auto_save = "✓ Enabled" if self.settings_manager.should_auto_save() else "✗ Disabled"
        confirm_delete = "✓ Enabled" if self.settings_manager.should_confirm_delete() else "✗ Disabled"
        show_tooltips = "✓ Enabled" if self.settings_manager.should_show_tooltips() else "✗ Disabled"
        
        ttk.Label(behavior_frame, text=f"Auto-save: {auto_save}", font=(font_family, font_size)).pack(anchor="w")
        ttk.Label(behavior_frame, text=f"Confirm Delete: {confirm_delete}", font=(font_family, font_size)).pack(anchor="w")
        ttk.Label(behavior_frame, text=f"Show Tooltips: {show_tooltips}", font=(font_family, font_size)).pack(anchor="w")
        
        # Window settings demo
        window_frame = ttk.LabelFrame(parent, text="Window Settings", padding=10)
        window_frame.pack(fill="x", pady=(0, 10))
        
        width, height = self.settings_manager.get_window_size()
        maximize = "✓ Yes" if self.settings_manager.should_maximize_on_startup() else "✗ No"
        
        ttk.Label(window_frame, text=f"Default Size: {width} x {height}", font=(font_family, font_size)).pack(anchor="w")
        ttk.Label(window_frame, text=f"Maximize on Startup: {maximize}", font=(font_family, font_size)).pack(anchor="w")
        
        # Backup settings demo
        backup_frame = ttk.LabelFrame(parent, text="Backup Settings", padding=10)
        backup_frame.pack(fill="x", pady=(0, 10))
        
        backup_settings = self.settings_manager.get_backup_settings()
        backup_enabled = "✓ Enabled" if backup_settings['enabled'] else "✗ Disabled"
        
        ttk.Label(backup_frame, text=f"Backup: {backup_enabled}", font=(font_family, font_size)).pack(anchor="w")
        ttk.Label(backup_frame, text=f"Frequency: {backup_settings['frequency']}", font=(font_family, font_size)).pack(anchor="w")
        ttk.Label(backup_frame, text=f"Location: {backup_settings['location']}", font=(font_family, font_size)).pack(anchor="w")
        
    def open_settings(self):
        """Open the settings window"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("Application Settings")
        settings_window.geometry("800x600")
        settings_window.transient(self.root)
        settings_window.grab_set()
        
        # Center the window
        settings_window.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 100,
            self.root.winfo_rooty() + 50
        ))
        
        def close_settings():
            settings_window.destroy()
            self.refresh_demo()
            self.status_var.set("Settings window closed - Demo refreshed")
        
        # Create settings frame
        settings_frame = ApplicationSettingsFrame(settings_window, close_settings)
        settings_frame.pack(fill="both", expand=True)
        
        self.status_var.set("Settings window opened")
        
    def apply_current_theme(self):
        """Apply the current theme setting"""
        try:
            current_theme = self.settings_manager.get_theme()
            
            # Create new window with the theme
            new_root = ttk.Window(themename=current_theme)
            new_root.title("Application Settings Demo")
            new_root.geometry("1000x700")
            
            # Close old window
            if hasattr(self, 'root'):
                self.root.destroy()
            
            self.root = new_root
            self.create_main_interface()
            
            self.status_var.set(f"Theme applied: {current_theme}")
            
        except Exception as e:
            if hasattr(self, 'status_var'):
                self.status_var.set(f"Error applying theme: {str(e)}")
            print(f"Error applying theme: {e}")
    
    def refresh_demo(self):
        """Refresh the demo to show current settings"""
        # Get the content frame
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, ttk.LabelFrame) and "Current Settings Demo" in child.cget("text"):
                        self.create_demo_widgets(child)
                        break
                break
        
        self.status_var.set("Demo refreshed with current settings")
    
    def test_settings(self):
        """Test various settings functionality"""
        test_results = []
        
        # Test currency formatting
        test_amount = 1234.56
        formatted = self.settings_manager.format_currency(test_amount)
        test_results.append(f"Currency Format Test: {formatted}")
        
        # Test date formatting
        test_date = datetime(2025, 1, 28)
        formatted_date = self.settings_manager.format_date(test_date)
        test_results.append(f"Date Format Test: {formatted_date}")
        
        # Test boolean settings
        auto_save = self.settings_manager.should_auto_save()
        test_results.append(f"Auto-save Setting: {auto_save}")
        
        confirm_delete = self.settings_manager.should_confirm_delete()
        test_results.append(f"Confirm Delete Setting: {confirm_delete}")
        
        # Test window settings
        width, height = self.settings_manager.get_window_size()
        test_results.append(f"Window Size Setting: {width}x{height}")
        
        # Test backup settings
        backup_settings = self.settings_manager.get_backup_settings()
        test_results.append(f"Backup Enabled: {backup_settings['enabled']}")
        test_results.append(f"Backup Frequency: {backup_settings['frequency']}")
        
        # Show results
        result_text = "\n".join(test_results)
        
        # Create test results window
        test_window = tk.Toplevel(self.root)
        test_window.title("Settings Test Results")
        test_window.geometry("500x400")
        test_window.transient(self.root)
        
        text_widget = tk.Text(test_window, wrap=tk.WORD, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(test_window, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.insert(1.0, f"Settings Test Results\n{'='*50}\n\n{result_text}")
        text_widget.configure(state="disabled")
        
        text_widget.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        self.status_var.set("Settings test completed - Check results window")
    
    def run(self):
        """Run the demo"""
        print("Starting Application Settings Demo...")
        print("Features demonstrated:")
        print("✓ Comprehensive settings management")
        print("✓ Theme selection and application")
        print("✓ Regional settings (date/currency formats)")
        print("✓ Behavior preferences")
        print("✓ Window and appearance settings")
        print("✓ Backup configuration")
        print("✓ Settings import/export")
        print("✓ Reset to defaults functionality")
        print("\nTry changing settings and see them applied in real-time!")
        
        self.root.mainloop()


if __name__ == "__main__":
    # Clean up any existing settings file for fresh demo
    if os.path.exists("settings.db"):
        backup_name = f"settings_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        os.rename("settings.db", backup_name)
        print(f"Existing settings backed up to: {backup_name}")
    
    print("Setting up demo environment...")
    
    app = SettingsDemo()
    app.run()
    
    # Cleanup option
    cleanup = input("\nDemo finished. Clean up demo settings? (y/n): ")
    if cleanup.lower() == 'y':
        if os.path.exists("settings.db"):
            os.remove("settings.db")
            print("Demo settings cleaned up!")
        print("Demo cleanup complete!")
