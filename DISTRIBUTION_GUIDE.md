# Cashbook Application Distribution Guide

This guide explains how to build and distribute the Cashbook application as a standalone Windows executable with an installer.

## Prerequisites

1. **Python Environment**: Make sure you have Python 3.x installed with all required dependencies.

2. **Required Python Packages**:
   ```
   pip install pyinstaller pillow ttkbootstrap matplotlib reportlab
   ```

3. **Inno Setup**: Download and install [Inno Setup](https://jrsoftware.org/isdl.php) to create the Windows installer.

## Building the Executable

### Step 1: Create the Application Icon

Run the `create_icon.py` script to generate an icon file for the application:

```
python create_icon.py
```

This will create `resources/icon.ico` from your existing logo or generate a default icon if no logo is found.

### Step 2: Build the Executable

Run the build script to create the executable:

```
build_exe.bat
```

This will:
1. Clean any previous build files
2. Build the application according to the specifications in `cashbook.spec`
3. Create the executable in the `dist/Cashbook` directory

### Step 3: Create the Installer

1. Open Inno Setup Compiler
2. Open the `cashbook_installer.iss` script
3. Click "Build" to compile the installer
4. The installer will be created in the `Output` directory as `Cashbook_Setup.exe`

## Troubleshooting Common Issues

### Missing Dependencies

If the application fails to start due to missing dependencies:

1. Check the PyInstaller output for warnings about missing modules
2. Add any missing modules to the `hiddenimports` list in `cashbook.spec`
3. Rebuild the executable

### File Not Found Errors

If the application can't find resource files:

1. Make sure all necessary files are included in the `datas` list in `cashbook.spec`
2. Check that file paths in the code use relative paths and `os.path.join()`
3. Rebuild the executable

### Database Connection Issues

If the application can't connect to the database:

1. Make sure the database file is included in the distribution
2. Check that the application is using the correct path to the database file
3. Ensure the application has write permissions to the installation directory

## Distribution Checklist

Before distributing the application to clients, verify:

- [ ] The application starts without errors
- [ ] All features work correctly
- [ ] The installer creates desktop and start menu shortcuts
- [ ] The application can be uninstalled properly
- [ ] The application works on a clean Windows installation
- [ ] All necessary documentation is included

## Additional Notes

### Silent Installation

For automated deployment, you can use the `/SILENT` or `/VERYSILENT` command-line parameters with the installer:

```
Cashbook_Setup.exe /SILENT
```

### Custom Installation Directory

To specify a custom installation directory:

```
Cashbook_Setup.exe /DIR="C:\CustomPath\Cashbook"
```

### Creating Updates

For application updates:

1. Increment the version number in `cashbook_installer.iss`
2. Rebuild the executable and installer
3. Distribute the new installer to clients

## Support

If you encounter any issues with the distribution process, please contact the development team for assistance.
