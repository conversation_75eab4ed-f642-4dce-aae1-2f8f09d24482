import tkinter as tk
from datetime import datetime, timedelta
from tkinter import filedialog, messagebox

import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.scrolled import Scrolled<PERSON>rame
from ttkbootstrap.tooltip import ToolTip

from model.client import Client
from model.invoice import Invoice
from model.invoice_template import InvoiceTemplate
from utils.email_service import EmailService
from utils.pdf_generator import InvoicePDFGenerator
from view.email_history_frame import EmailHistoryFrame
from view.email_settings_frame import EmailSettingsFrame
from view.email_templates_frame import EmailTemplatesFrame
from view.invoice_email_dialog import InvoiceEmailDialog
from view.invoice_entry_frame import InvoiceEntryFrame
from view.invoice_template_frame import InvoiceTemplateFrame


class InvoiceDashboardFrame(ttk.Frame):
    """Dashboard for managing invoices"""

    def __init__(self, parent, company_name, db_path, close_callback=None):
        super().__init__(parent)
        self.parent = parent
        self.company_name = company_name
        self.db_path = db_path
        self.close_callback = close_callback
        self.title = f"Invoices - {company_name}"

        # Initialize models
        self.invoice_model = Invoice(db_path)
        self.client_model = Client(db_path)
        self.template_model = InvoiceTemplate(db_path)
        self.email_service = EmailService(db_path)

        # Check for overdue invoices
        self.check_overdue_invoices()

        # Create UI
        self.create_widgets()

        # Load invoices
        self.load_invoices()

    def create_widgets(self):
        """Create the UI widgets"""
        # Main frame
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill="both", expand=True)

        # Header with title and close button
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))

        title_label = ttk.Label(header_frame, text="Invoice Management", font=("Segoe UI", 16, "bold"))
        title_label.pack(side="left")

        if self.close_callback:
            close_button = ttk.Button(header_frame, text="Close", command=self.close_callback, bootstyle=DANGER)
            close_button.pack(side="right")

        # Action buttons
        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill="x", pady=(0, 10))

        new_invoice_button = ttk.Button(action_frame, text="Create New Invoice",
                                      command=self.create_invoice, bootstyle=SUCCESS)
        new_invoice_button.pack(side="left", padx=(0, 10))

        templates_button = ttk.Button(action_frame, text="Manage Templates",
                                    command=self.manage_templates, bootstyle=INFO)
        templates_button.pack(side="left", padx=(0, 10))

        # Email buttons
        email_frame = ttk.Frame(action_frame)
        email_frame.pack(side="left", padx=(0, 10))

        email_settings_button = ttk.Button(email_frame, text="Email Settings",
                                         command=self.open_email_settings, bootstyle=INFO)
        email_settings_button.pack(side="left", padx=(0, 5))

        email_templates_button = ttk.Button(email_frame, text="Email Templates",
                                          command=self.open_email_templates, bootstyle=INFO)
        email_templates_button.pack(side="left", padx=(0, 5))

        email_history_button = ttk.Button(email_frame, text="Email History",
                                        command=self.open_email_history, bootstyle=INFO)
        email_history_button.pack(side="left")

        # Search and filter frame
        search_filter_frame = ttk.Frame(action_frame)
        search_filter_frame.pack(side="right")

        # Search box
        search_frame = ttk.LabelFrame(search_filter_frame, text="Search", padding=5)
        search_frame.pack(side="left", padx=(0, 10))

        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side="left", padx=(0, 5))
        search_entry.bind("<Return>", lambda e: self.search_invoices())

        search_button = ttk.Button(search_frame, text="Search",
                                 command=self.search_invoices, bootstyle=INFO)
        search_button.pack(side="left")

        # Filter options
        filter_frame = ttk.LabelFrame(search_filter_frame, text="Filter", padding=5)
        filter_frame.pack(side="left")

        # Status filter
        ttk.Label(filter_frame, text="Status:").pack(side="left", padx=(0, 5))

        self.status_var = tk.StringVar(value="all")
        status_values = ["all", Invoice.STATUS_DRAFT, Invoice.STATUS_SENT,
                        Invoice.STATUS_PAID, Invoice.STATUS_OVERDUE,
                        Invoice.STATUS_CANCELLED]

        status_combobox = ttk.Combobox(filter_frame, textvariable=self.status_var,
                                      values=status_values, state="readonly", width=10)
        status_combobox.pack(side="left", padx=(0, 10))

        # Date range filter
        date_frame = ttk.Frame(filter_frame)
        date_frame.pack(side="left", padx=(0, 10))

        self.date_filter_var = tk.StringVar(value="all")
        date_values = ["all", "this_month", "last_month", "this_year", "overdue"]
        date_labels = ["All Dates", "This Month", "Last Month", "This Year", "Overdue"]

        date_dropdown = ttk.Combobox(date_frame, textvariable=self.date_filter_var,
                                   values=date_labels, state="readonly", width=12)
        date_dropdown.pack(side="left")

        # Create mapping between labels and values
        self.date_filter_mapping = dict(zip(date_labels, date_values))

        # Apply filter button
        apply_filter_button = ttk.Button(filter_frame, text="Apply",
                                       command=self.load_invoices, bootstyle=INFO)
        apply_filter_button.pack(side="left")

        # Reset filter button
        reset_filter_button = ttk.Button(filter_frame, text="Reset",
                                       command=self.reset_filters, bootstyle=SECONDARY)
        reset_filter_button.pack(side="left", padx=(5, 0))

        # Create notebook for different invoice views
        self.invoice_notebook = ttk.Notebook(main_frame)
        self.invoice_notebook.pack(fill="both", expand=True)

        # Create frames for each tab
        all_invoices_tab = ttk.Frame(self.invoice_notebook)
        draft_tab = ttk.Frame(self.invoice_notebook)
        sent_tab = ttk.Frame(self.invoice_notebook)
        overdue_tab = ttk.Frame(self.invoice_notebook)

        # Add tabs to notebook
        self.invoice_notebook.add(all_invoices_tab, text="All Invoices")
        self.invoice_notebook.add(draft_tab, text="Draft")
        self.invoice_notebook.add(sent_tab, text="Sent")
        self.invoice_notebook.add(overdue_tab, text="Overdue")

        # Create treeview for each tab
        self.create_invoice_treeview(all_invoices_tab, "all")
        self.create_invoice_treeview(draft_tab, Invoice.STATUS_DRAFT)
        self.create_invoice_treeview(sent_tab, Invoice.STATUS_SENT)
        self.create_invoice_treeview(overdue_tab, Invoice.STATUS_OVERDUE)

        # Bind tab change event
        self.invoice_notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

    def create_invoice_treeview(self, parent, status):
        """Create a treeview for displaying invoices

        Args:
            parent: Parent widget
            status: Invoice status to filter by
        """
        # Create frame for treeview
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Create scrollbars
        scrollbar_y = ttk.Scrollbar(tree_frame, orient="vertical")
        scrollbar_x = ttk.Scrollbar(tree_frame, orient="horizontal")

        # Create treeview
        columns = ("id", "invoice_number", "client", "issue_date", "due_date",
                  "status", "total_amount")

        treeview = ttk.Treeview(
            tree_frame,
            columns=columns,
            show="headings",
            yscrollcommand=scrollbar_y.set,
            xscrollcommand=scrollbar_x.set
        )

        # Configure scrollbars
        scrollbar_y.configure(command=treeview.yview)
        scrollbar_x.configure(command=treeview.xview)

        # Configure columns
        treeview.heading("id", text="ID")
        treeview.heading("invoice_number", text="Invoice #")
        treeview.heading("client", text="Client")
        treeview.heading("issue_date", text="Issue Date")
        treeview.heading("due_date", text="Due Date")
        treeview.heading("status", text="Status")
        treeview.heading("total_amount", text="Total")

        treeview.column("id", width=50, stretch=False)
        treeview.column("invoice_number", width=100)
        treeview.column("client", width=200)
        treeview.column("issue_date", width=100)
        treeview.column("due_date", width=100)
        treeview.column("status", width=100)
        treeview.column("total_amount", width=100, anchor="e")

        # Pack treeview and scrollbars
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        treeview.pack(fill="both", expand=True)

        # Store treeview reference
        setattr(self, f"invoice_tree_{status}", treeview)

        # Bind events
        treeview.bind("<Double-1>", lambda e, s=status: self.on_invoice_double_click(e, s))
        treeview.bind("<Return>", lambda e, s=status: self.on_invoice_double_click(e, s))

        # Create context menu
        context_menu = tk.Menu(treeview, tearoff=0)
        context_menu.add_command(label="Edit Invoice",
                                command=lambda s=status: self.edit_selected_invoice(s))
        context_menu.add_command(label="View/Print PDF",
                                command=lambda s=status: self.generate_pdf(s))
        context_menu.add_command(label="Duplicate Invoice",
                                command=lambda s=status: self.duplicate_invoice(s))
        context_menu.add_separator()

        # Email submenu
        email_menu = tk.Menu(context_menu, tearoff=0)
        email_menu.add_command(label="Send Invoice Email",
                             command=lambda s=status: self.send_invoice_email(s))
        email_menu.add_command(label="View Email History",
                             command=lambda s=status: self.view_email_history(s))
        context_menu.add_cascade(label="Email", menu=email_menu)

        context_menu.add_separator()
        context_menu.add_command(label="Mark as Sent",
                                command=lambda s=status: self.mark_as_sent(s))
        context_menu.add_command(label="Mark as Paid",
                                command=lambda s=status: self.mark_as_paid(s))
        context_menu.add_command(label="Mark as Overdue",
                                command=lambda s=status: self.mark_as_overdue(s))
        context_menu.add_separator()
        context_menu.add_command(label="Cancel Invoice",
                                command=lambda s=status: self.cancel_invoice(s))

        # Store context menu reference
        setattr(self, f"context_menu_{status}", context_menu)

        # Bind right-click event
        treeview.bind("<Button-3>", lambda e, s=status: self.show_context_menu(e, s))

    def load_invoices(self):
        """Load invoices into the treeviews"""
        # Get filter status
        filter_status = self.status_var.get()
        if filter_status == "all":
            filter_status = None

        # Get date filter
        date_filter = None
        if hasattr(self, 'date_filter_var'):
            date_label = self.date_filter_var.get()
            date_filter = self.date_filter_mapping.get(date_label)
            if date_filter == "all":
                date_filter = None

        # Get search term
        search_term = None
        if hasattr(self, 'search_var') and self.search_var.get():
            search_term = self.search_var.get()

        # Get all invoices
        invoices = self.invoice_model.get_all_invoices(status=filter_status)

        # Apply date filter if needed
        if date_filter:
            today = datetime.now().date()
            filtered_invoices = []

            for invoice in invoices:
                issue_date = datetime.strptime(invoice['issue_date'], "%Y-%m-%d").date()
                due_date = datetime.strptime(invoice['due_date'], "%Y-%m-%d").date()

                if date_filter == "this_month":
                    # This month
                    if issue_date.year == today.year and issue_date.month == today.month:
                        filtered_invoices.append(invoice)
                elif date_filter == "last_month":
                    # Last month
                    last_month = today.month - 1
                    year = today.year
                    if last_month == 0:
                        last_month = 12
                        year -= 1
                    if issue_date.year == year and issue_date.month == last_month:
                        filtered_invoices.append(invoice)
                elif date_filter == "this_year":
                    # This year
                    if issue_date.year == today.year:
                        filtered_invoices.append(invoice)
                elif date_filter == "overdue":
                    # Overdue
                    if due_date < today and invoice['status'] != Invoice.STATUS_PAID:
                        filtered_invoices.append(invoice)

            invoices = filtered_invoices

        # Apply search filter if needed
        if search_term:
            search_term = search_term.lower()
            filtered_invoices = []

            for invoice in invoices:
                # Search in invoice number, client name, and client company
                if (search_term in invoice['invoice_number'].lower() or
                    search_term in invoice.get('client_name', '').lower() or
                    search_term in invoice.get('client_company', '').lower() or
                    search_term in invoice.get('notes', '').lower() or
                    search_term in str(invoice['total_amount']).lower()):
                    filtered_invoices.append(invoice)

            invoices = filtered_invoices

        # Clear existing items in all treeviews
        for status in ["all", Invoice.STATUS_DRAFT, Invoice.STATUS_SENT, Invoice.STATUS_OVERDUE]:
            treeview = getattr(self, f"invoice_tree_{status}")
            for item in treeview.get_children():
                treeview.delete(item)

        # Show message if no invoices found
        if not invoices:
            for status in ["all", Invoice.STATUS_DRAFT, Invoice.STATUS_SENT, Invoice.STATUS_OVERDUE]:
                treeview = getattr(self, f"invoice_tree_{status}")
                treeview.insert("", "end", values=("", "No invoices found", "", "", "", "", ""))
            return

        # Add invoices to appropriate treeviews
        for invoice in invoices:
            # Format values
            client_name = invoice.get("client_name", "")
            if invoice.get("client_company"):
                client_name += f" ({invoice['client_company']})"

            # Format dates for display
            issue_date = invoice["issue_date"]
            due_date = invoice["due_date"]

            # Add visual indicator for overdue invoices
            status_display = invoice["status"]
            if invoice["status"] == Invoice.STATUS_OVERDUE:
                status_display = "⚠️ " + status_display

            values = (
                invoice["id"],
                invoice["invoice_number"],
                client_name,
                issue_date,
                due_date,
                status_display,
                f"${invoice['total_amount']:.2f}"
            )

            # Add to all invoices treeview
            item_id = self.invoice_tree_all.insert("", "end", values=values)

            # Set tag for overdue invoices
            if invoice["status"] == Invoice.STATUS_OVERDUE:
                self.invoice_tree_all.tag_configure("overdue", foreground="red")
                self.invoice_tree_all.item(item_id, tags=("overdue",))

            # Add to status-specific treeview if applicable
            if invoice["status"] == Invoice.STATUS_DRAFT:
                self.invoice_tree_draft.insert("", "end", values=values)
            elif invoice["status"] == Invoice.STATUS_SENT:
                item_id = self.invoice_tree_sent.insert("", "end", values=values)

                # Check if due date is approaching (within 7 days)
                try:
                    due_date_obj = datetime.strptime(due_date, "%Y-%m-%d").date()
                    today = datetime.now().date()
                    days_until_due = (due_date_obj - today).days

                    if 0 < days_until_due <= 7:
                        self.invoice_tree_sent.tag_configure("approaching", foreground="orange")
                        self.invoice_tree_sent.item(item_id, tags=("approaching",))
                except:
                    pass

            elif invoice["status"] == Invoice.STATUS_OVERDUE:
                item_id = self.invoice_tree_overdue.insert("", "end", values=values)
                self.invoice_tree_overdue.tag_configure("overdue", foreground="red")
                self.invoice_tree_overdue.item(item_id, tags=("overdue",))

    def on_tab_changed(self, event):
        """Handle tab change event"""
        # Get the selected tab
        tab_id = self.invoice_notebook.index("current")

        # Update status filter based on selected tab
        if tab_id == 0:
            self.status_var.set("all")
        elif tab_id == 1:
            self.status_var.set(Invoice.STATUS_DRAFT)
        elif tab_id == 2:
            self.status_var.set(Invoice.STATUS_SENT)
        elif tab_id == 3:
            self.status_var.set(Invoice.STATUS_OVERDUE)

    def create_invoice(self):
        """Open the invoice entry form to create a new invoice"""
        # Get the main application container
        main_app = self.parent

        # Create invoice entry frame
        invoice_entry_key = f"invoice_entry_{self.company_name}"

        if invoice_entry_key in main_app.screens:
            # Remove existing invoice entry frame
            main_app.remove_screen(invoice_entry_key)

        # Create new invoice entry frame
        invoice_entry_frame = InvoiceEntryFrame(
            main_app,
            self.company_name,
            self.db_path,
            self.return_to_invoices
        )
        main_app.add_screen(invoice_entry_key, invoice_entry_frame)

        # Show invoice entry frame
        main_app.show_screen(invoice_entry_key)

    def edit_selected_invoice(self, status):
        """Edit the selected invoice"""
        # Get the treeview for the current status
        treeview = getattr(self, f"invoice_tree_{status}")

        selected_item = treeview.selection()
        if not selected_item:
            messagebox.showwarning("No Selection", "Please select an invoice to edit.")
            return

        # Get invoice ID
        invoice_id = treeview.item(selected_item[0], "values")[0]

        # Open invoice entry frame
        self.edit_invoice(invoice_id)

    def edit_invoice(self, invoice_id):
        """Open the invoice entry form to edit an existing invoice"""
        # Get the main application container
        main_app = self.parent

        # Create invoice entry frame
        invoice_entry_key = f"invoice_entry_{self.company_name}_{invoice_id}"

        if invoice_entry_key in main_app.screens:
            # Remove existing invoice entry frame
            main_app.remove_screen(invoice_entry_key)

        # Create new invoice entry frame
        invoice_entry_frame = InvoiceEntryFrame(
            main_app,
            self.company_name,
            self.db_path,
            self.return_to_invoices,
            invoice_id
        )
        main_app.add_screen(invoice_entry_key, invoice_entry_frame)

        # Show invoice entry frame
        main_app.show_screen(invoice_entry_key)

    def return_to_invoices(self):
        """Return to the invoice dashboard"""
        # Get the main application container
        main_app = self.parent

        # Show invoice dashboard
        main_app.show_screen(f"invoice_dashboard_{self.company_name}")

        # Reload invoices
        self.load_invoices()

    def on_invoice_double_click(self, event, status):
        """Handle double click on invoice"""
        self.edit_selected_invoice(status)

    def show_context_menu(self, event, status):
        """Show context menu on right click"""
        # Get the treeview for the current status
        treeview = getattr(self, f"invoice_tree_{status}")
        context_menu = getattr(self, f"context_menu_{status}")

        # Select the item under the cursor
        item = treeview.identify_row(event.y)
        if item:
            treeview.selection_set(item)
            context_menu.post(event.x_root, event.y_root)

    def update_invoice_status(self, status, new_status):
        """Update the status of the selected invoice"""
        # Get the treeview for the current status
        treeview = getattr(self, f"invoice_tree_{status}")

        selected_item = treeview.selection()
        if not selected_item:
            messagebox.showwarning("No Selection", "Please select an invoice.")
            return

        # Get invoice ID and current status
        values = treeview.item(selected_item[0], "values")
        invoice_id = values[0]
        current_status = values[5]

        # Check if status change is valid
        if current_status == new_status:
            messagebox.showinfo("No Change", f"Invoice is already marked as {new_status}.")
            return

        # Confirm status change
        if not messagebox.askyesno("Confirm Status Change",
                                  f"Are you sure you want to mark this invoice as {new_status}?"):
            return

        # Update invoice status
        try:
            self.invoice_model.update_invoice_status(invoice_id, new_status)
            messagebox.showinfo("Success", f"Invoice status updated to {new_status}.")

            # Reload invoices
            self.load_invoices()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update invoice status: {str(e)}")

    def mark_as_sent(self, status):
        """Mark the selected invoice as sent"""
        self.update_invoice_status(status, Invoice.STATUS_SENT)

    def mark_as_paid(self, status):
        """Mark the selected invoice as paid"""
        self.update_invoice_status(status, Invoice.STATUS_PAID)

    def mark_as_overdue(self, status):
        """Mark the selected invoice as overdue"""
        self.update_invoice_status(status, Invoice.STATUS_OVERDUE)

    def cancel_invoice(self, status):
        """Cancel the selected invoice"""
        self.update_invoice_status(status, Invoice.STATUS_CANCELLED)

    def search_invoices(self):
        """Search invoices based on the search term"""
        self.load_invoices()

    def reset_filters(self):
        """Reset all filters to default values"""
        self.status_var.set("all")
        self.date_filter_var.set("All Dates")
        self.search_var.set("")
        self.load_invoices()

    def check_overdue_invoices(self):
        """Check for overdue invoices and update their status"""
        try:
            # Update overdue invoices
            count = self.invoice_model.check_overdue_invoices()

            # Show notification if any invoices were marked as overdue
            if count > 0:
                messagebox.showinfo("Overdue Invoices",
                                  f"{count} invoice(s) have been marked as overdue.")
        except Exception as e:
            print(f"Error checking overdue invoices: {str(e)}")

    def manage_templates(self):
        """Open the template management screen"""
        # Get the main application container
        main_app = self.parent

        # Create template management frame
        template_key = f"invoice_templates_{self.company_name}"

        if template_key in main_app.screens:
            # Remove existing template management frame
            main_app.remove_screen(template_key)

        # Create new template management frame
        template_frame = InvoiceTemplateFrame(
            main_app,
            self.company_name,
            self.db_path,
            self.return_to_invoices
        )
        main_app.add_screen(template_key, template_frame)

        # Show template management frame
        main_app.show_screen(template_key)

    def generate_pdf(self, status):
        """Generate a PDF for the selected invoice"""
        # Get the treeview for the current status
        treeview = getattr(self, f"invoice_tree_{status}")

        selected_item = treeview.selection()
        if not selected_item:
            messagebox.showwarning("No Selection", "Please select an invoice to generate PDF.")
            return

        # Get invoice ID
        invoice_id = treeview.item(selected_item[0], "values")[0]

        # Get invoice data
        invoice = self.invoice_model.get_invoice(invoice_id)
        if not invoice:
            messagebox.showerror("Error", f"Invoice with ID {invoice_id} not found")
            return

        # Get default template
        template = self.template_model.get_default_template()
        if not template:
            messagebox.showerror("Error", "No default template found. Please create a template first.")
            return

        # Ask for save location
        file_path = filedialog.asksaveasfilename(
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf")],
            initialfile=f"Invoice_{invoice['invoice_number']}.pdf"
        )

        if not file_path:
            return  # User cancelled

        try:
            # Generate PDF
            pdf_generator = InvoicePDFGenerator(template, invoice, file_path)
            pdf_path = pdf_generator.generate_pdf()

            # Ask if user wants to open the PDF
            if messagebox.askyesno("PDF Generated",
                                 f"PDF invoice has been generated successfully.\n\n"
                                 f"Would you like to open it now?"):
                # Open the PDF
                import os
                import platform
                import subprocess

                if platform.system() == 'Windows':
                    os.startfile(pdf_path)
                elif platform.system() == 'Darwin':  # macOS
                    subprocess.call(['open', pdf_path])
                else:  # Linux
                    subprocess.call(['xdg-open', pdf_path])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate PDF: {str(e)}")

    def duplicate_invoice(self, status):
        """Duplicate the selected invoice"""
        # Get the treeview for the current status
        treeview = getattr(self, f"invoice_tree_{status}")

        selected_item = treeview.selection()
        if not selected_item:
            messagebox.showwarning("No Selection", "Please select an invoice to duplicate.")
            return

        # Get invoice ID
        invoice_id = treeview.item(selected_item[0], "values")[0]

        # Get invoice data
        invoice = self.invoice_model.get_invoice(invoice_id)
        if not invoice:
            messagebox.showerror("Error", f"Invoice with ID {invoice_id} not found")
            return

        # Confirm duplication
        if not messagebox.askyesno("Confirm Duplication",
                                 f"Are you sure you want to duplicate invoice {invoice['invoice_number']}?\n\n"
                                 f"A new invoice will be created with the same details but a new invoice number."):
            return

        try:
            # Create a copy of the invoice data
            new_invoice = invoice.copy()

            # Remove ID and set status to draft
            new_invoice.pop('id', None)
            new_invoice['status'] = Invoice.STATUS_DRAFT

            # Generate a new invoice number
            new_invoice['invoice_number'] = self.invoice_model.generate_invoice_number()

            # Update dates
            from datetime import datetime, timedelta
            today = datetime.now().date()
            new_invoice['issue_date'] = today.strftime("%Y-%m-%d")

            # Set due date to 30 days from today
            due_date = today + timedelta(days=30)
            new_invoice['due_date'] = due_date.strftime("%Y-%m-%d")

            # Add note about duplication
            notes = new_invoice.get('notes', '')
            if notes:
                notes += "\n\n"
            notes += f"Duplicated from invoice {invoice['invoice_number']} on {today.strftime('%Y-%m-%d')}"
            new_invoice['notes'] = notes

            # Create the new invoice
            new_id = self.invoice_model.create_invoice(
                new_invoice['client_id'],
                new_invoice['invoice_number'],
                new_invoice['issue_date'],
                new_invoice['due_date'],
                new_invoice['notes']
            )

            # Add invoice items
            for item in invoice['items']:
                item_copy = item.copy()
                item_copy.pop('id', None)
                item_copy.pop('invoice_id', None)

                self.invoice_model.add_invoice_item(
                    new_id,
                    item_copy['description'],
                    item_copy['quantity'],
                    item_copy['unit_price']
                )

            # Show success message
            messagebox.showinfo("Success",
                              f"Invoice duplicated successfully.\n\n"
                              f"New invoice number: {new_invoice['invoice_number']}")

            # Reload invoices
            self.load_invoices()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to duplicate invoice: {str(e)}")

    def send_invoice_email(self, status):
        """Send an email for the selected invoice"""
        # Get the treeview for the current status
        treeview = getattr(self, f"invoice_tree_{status}")

        selected_item = treeview.selection()
        if not selected_item:
            messagebox.showwarning("No Selection", "Please select an invoice to email.")
            return

        # Get invoice ID
        invoice_id = treeview.item(selected_item[0], "values")[0]

        # Check if email settings are configured
        settings = self.email_service.get_email_settings()
        if not settings:
            if messagebox.askyesno("Email Settings Required",
                                 "Email settings are not configured. Would you like to configure them now?"):
                self.open_email_settings()
            return

        # Open email dialog
        email_dialog = InvoiceEmailDialog(self, self.db_path, invoice_id, self.load_invoices)

    def view_email_history(self, status):
        """View email history for the selected invoice"""
        # Get the treeview for the current status
        treeview = getattr(self, f"invoice_tree_{status}")

        selected_item = treeview.selection()
        if not selected_item:
            messagebox.showwarning("No Selection", "Please select an invoice to view email history.")
            return

        # Get invoice ID
        invoice_id = treeview.item(selected_item[0], "values")[0]

        # Get the main application container
        main_app = self.parent

        # Create email history frame
        history_key = f"email_history_{self.company_name}_{invoice_id}"

        if history_key in main_app.screens:
            # Remove existing email history frame
            main_app.remove_screen(history_key)

        # Create new email history frame
        history_frame = EmailHistoryFrame(
            main_app,
            self.db_path,
            invoice_id,
            self.return_to_invoices
        )
        main_app.add_screen(history_key, history_frame)

        # Show email history frame
        main_app.show_screen(history_key)

    def open_email_settings(self):
        """Open the email settings screen"""
        # Get the main application container
        main_app = self.parent

        # Create email settings frame
        settings_key = f"email_settings_{self.company_name}"

        if settings_key in main_app.screens:
            # Remove existing email settings frame
            main_app.remove_screen(settings_key)

        # Create new email settings frame
        settings_frame = EmailSettingsFrame(
            main_app,
            self.db_path,
            self.return_to_invoices
        )
        main_app.add_screen(settings_key, settings_frame)

        # Show email settings frame
        main_app.show_screen(settings_key)

    def open_email_templates(self):
        """Open the email templates screen"""
        # Get the main application container
        main_app = self.parent

        # Create email templates frame
        templates_key = f"email_templates_{self.company_name}"

        if templates_key in main_app.screens:
            # Remove existing email templates frame
            main_app.remove_screen(templates_key)

        # Create new email templates frame
        templates_frame = EmailTemplatesFrame(
            main_app,
            self.db_path,
            self.return_to_invoices
        )
        main_app.add_screen(templates_key, templates_frame)

        # Show email templates frame
        main_app.show_screen(templates_key)

    def open_email_history(self):
        """Open the email history screen"""
        # Get the main application container
        main_app = self.parent

        # Create email history frame
        history_key = f"email_history_{self.company_name}"

        if history_key in main_app.screens:
            # Remove existing email history frame
            main_app.remove_screen(history_key)

        # Create new email history frame
        history_frame = EmailHistoryFrame(
            main_app,
            self.db_path,
            None,  # No specific invoice
            self.return_to_invoices
        )
        main_app.add_screen(history_key, history_frame)

        # Show email history frame
        main_app.show_screen(history_key)
