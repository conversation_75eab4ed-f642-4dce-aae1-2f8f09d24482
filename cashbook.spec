# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],  # main Python script
    pathex=[],
    binaries=[],
    datas=[
        # Include all necessary data files
        ('resources', 'resources'),  # Resources folder with images, icons, etc.
        # Include database files
        ('*.db', '.'),               # Include all database files in the root directory
        # Include application modules
        ('model', 'model'),          # Model directory
        ('view', 'view'),            # View directory
        ('controller', 'controller'),# Controller directory
        ('utils', 'utils'),          # Utils directory
        ('reports', 'reports'),      # Reports directory
    ],
    hiddenimports=[
        # GUI libraries
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'ttkbootstrap',
        'ttkbootstrap.constants',
        'ttkbootstrap.dialogs',
        'ttkbootstrap.scrolled',
        'ttkbootstrap.tooltip',
        'ttkbootstrap.toast',
        # Database
        'sqlite3',
        # Data visualization
        'matplotlib',
        'matplotlib.backends.backend_tkagg',
        'numpy',
        # PDF generation
        'reportlab',
        'reportlab.lib',
        'reportlab.pdfgen',
        'reportlab.platypus',
        # Image processing
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        # Standard libraries that might be needed
        'datetime',
        'os',
        'sys',
        'json',
        'csv',
        're',
        'time',
        'calendar',
        'math',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(
    a.pure,
    a.zipped_data,
    cipher=block_cipher
)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Cashbook',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to False for a GUI application without console
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/icon.ico',  # Path to application icon
    version='file_version_info.txt'  # File version info with author details
)
