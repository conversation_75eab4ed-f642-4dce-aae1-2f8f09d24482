#!/usr/bin/env python3
"""
Demo script to showcase the modern client dashboard
"""

import tkinter as tk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import os
import sqlite3
from datetime import datetime

# Import the modern client frame
from view.client_frame import ClientFrame


class DemoApp:
    def __init__(self):
        # Create main window
        self.root = ttk.Window(themename="cosmo")
        self.root.title("Modern Cashbook Dashboard Demo")
        self.root.geometry("1200x800")
        
        # Create some demo companies if they don't exist
        self.create_demo_companies()
        
        # Create the modern client frame
        self.client_frame = ClientFrame(
            parent=self.root,
            username="Demo User",
            logout_callback=self.logout,
            create_company_callback=self.create_company,
            open_company_callback=self.open_company
        )
        
        self.client_frame.pack(fill="both", expand=True)
        
    def create_demo_companies(self):
        """Create some demo companies for testing"""
        demo_companies = [
            {
                'name': 'Tech Solutions Inc',
                'transactions': 150,
                'balance': 25000.50,
                'created': '2024-01-15',
                'last_accessed': '2025-01-26 14:30:00'
            },
            {
                'name': 'Green Energy Co',
                'transactions': 89,
                'balance': -1500.75,
                'created': '2024-03-22',
                'last_accessed': '2025-01-25 09:15:00'
            },
            {
                'name': 'Local Restaurant',
                'transactions': 234,
                'balance': 8750.25,
                'created': '2024-06-10',
                'last_accessed': '2025-01-24 16:45:00'
            },
            {
                'name': 'Consulting Services',
                'transactions': 67,
                'balance': 15000.00,
                'created': '2024-09-05',
                'last_accessed': 'Never'
            }
        ]
        
        for company in demo_companies:
            db_file = f"{company['name'].lower().replace(' ', '_')}.db"
            
            # Skip if already exists
            if os.path.exists(db_file):
                continue
                
            try:
                # Create database
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # Create basic tables
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS accounts (
                        id INTEGER PRIMARY KEY,
                        name TEXT NOT NULL,
                        type TEXT NOT NULL,
                        currency TEXT NOT NULL DEFAULT 'USD',
                        opening_balance REAL NOT NULL DEFAULT 0,
                        current_balance REAL NOT NULL DEFAULT 0,
                        description TEXT,
                        created_date TEXT NOT NULL
                    )
                ''')
                
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS transactions (
                        id INTEGER PRIMARY KEY,
                        date TEXT NOT NULL,
                        description TEXT NOT NULL,
                        amount REAL NOT NULL,
                        type TEXT NOT NULL,
                        category TEXT,
                        account_id INTEGER,
                        reconciled BOOLEAN DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (account_id) REFERENCES accounts (id)
                    )
                ''')
                
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS metadata (
                        key TEXT PRIMARY KEY,
                        value TEXT
                    )
                ''')
                
                # Insert metadata
                cursor.execute(
                    "INSERT INTO metadata (key, value) VALUES (?, ?)",
                    ("created_date", company['created'])
                )
                
                cursor.execute(
                    "INSERT INTO metadata (key, value) VALUES (?, ?)",
                    ("company_name", company['name'])
                )
                
                if company['last_accessed'] != 'Never':
                    cursor.execute(
                        "INSERT INTO metadata (key, value) VALUES (?, ?)",
                        ("last_accessed", company['last_accessed'])
                    )
                
                # Create a main account
                cursor.execute('''
                    INSERT INTO accounts (name, type, currency, opening_balance, current_balance, created_date)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', ("Main Account", "Checking Account", "USD", 0, company['balance'], company['created']))
                
                # Create some demo transactions
                for i in range(company['transactions']):
                    cursor.execute('''
                        INSERT INTO transactions (date, description, amount, type, category, account_id)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        company['created'],
                        f"Demo Transaction {i+1}",
                        100.0 if i % 2 == 0 else -50.0,
                        "Income" if i % 2 == 0 else "Expense",
                        "Demo Category",
                        1
                    ))
                
                conn.commit()
                conn.close()
                
                print(f"Created demo company: {company['name']}")
                
            except Exception as e:
                print(f"Error creating demo company {company['name']}: {e}")
    
    def logout(self):
        """Handle logout"""
        print("Logout clicked")
        self.root.quit()
    
    def create_company(self):
        """Handle create company"""
        print("Create company clicked")
        # Simple dialog for demo
        from tkinter import simpledialog
        name = simpledialog.askstring("Create Company", "Enter company name:")
        if name:
            # Create a simple demo company
            db_file = f"{name.lower().replace(' ', '_')}.db"
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS accounts (
                        id INTEGER PRIMARY KEY,
                        name TEXT NOT NULL,
                        type TEXT NOT NULL,
                        currency TEXT NOT NULL DEFAULT 'USD',
                        opening_balance REAL NOT NULL DEFAULT 0,
                        current_balance REAL NOT NULL DEFAULT 0,
                        description TEXT,
                        created_date TEXT NOT NULL
                    )
                ''')
                
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS transactions (
                        id INTEGER PRIMARY KEY,
                        date TEXT NOT NULL,
                        description TEXT NOT NULL,
                        amount REAL NOT NULL,
                        type TEXT NOT NULL,
                        category TEXT,
                        account_id INTEGER,
                        reconciled BOOLEAN DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (account_id) REFERENCES accounts (id)
                    )
                ''')
                
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS metadata (
                        key TEXT PRIMARY KEY,
                        value TEXT
                    )
                ''')
                
                # Insert metadata
                current_time = datetime.now().strftime("%Y-%m-%d")
                cursor.execute(
                    "INSERT INTO metadata (key, value) VALUES (?, ?)",
                    ("created_date", current_time)
                )
                
                cursor.execute(
                    "INSERT INTO metadata (key, value) VALUES (?, ?)",
                    ("company_name", name)
                )
                
                # Create a main account
                cursor.execute('''
                    INSERT INTO accounts (name, type, currency, opening_balance, current_balance, created_date)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', ("Main Account", "Checking Account", "USD", 0, 0, current_time))
                
                conn.commit()
                conn.close()
                
                # Refresh the dashboard
                self.client_frame.load_companies()
                
            except Exception as e:
                print(f"Error creating company: {e}")
    
    def open_company(self, company_name):
        """Handle open company"""
        print(f"Opening company: {company_name}")
        # For demo, just show a message
        from tkinter import messagebox
        messagebox.showinfo("Demo", f"Would open company: {company_name}\n\nThis is a demo of the modern dashboard interface.")
    
    def run(self):
        """Run the demo"""
        print("Starting Modern Cashbook Dashboard Demo...")
        print("Features demonstrated:")
        print("✓ Modern card-based company display")
        print("✓ Last-used sorting")
        print("✓ Hover effects on cards")
        print("✓ Quick statistics")
        print("✓ Responsive design")
        print("✓ Empty state handling")
        print("✓ Sort options (Last Used, Name, Created Date, Balance)")
        print("\nTry the different sort options and hover over the company cards!")
        
        self.root.mainloop()


if __name__ == "__main__":
    # Clean up any existing demo files
    demo_files = [
        'tech_solutions_inc.db',
        'green_energy_co.db', 
        'local_restaurant.db',
        'consulting_services.db'
    ]
    
    print("Setting up demo environment...")
    
    app = DemoApp()
    app.run()
    
    # Cleanup option
    cleanup = input("\nDemo finished. Clean up demo files? (y/n): ")
    if cleanup.lower() == 'y':
        for file in demo_files:
            if os.path.exists(file):
                os.remove(file)
                print(f"Removed {file}")
        print("Demo cleanup complete!")
